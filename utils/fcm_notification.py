import time
import asyncio
import datetime
import logging
import json
import firebase_admin
from firebase_admin import credentials, messaging
from aiohttp import ClientSession
import requests
import argparse
from pyfcm import FCMNotification
from pyfcm.errors import FCMServerError
import firebase_admin
from firebase_admin import credentials, messaging
from google.oauth2 import service_account
import google.auth.transport.requests
import time, json, httpx, jwt
from pathlib import Path

# تنظیمات لاگ‌گذاری
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# تنظیمات Firebase
data = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PROJECT_ID = 'habib-73dad'
BASE_URL = 'https://fcm.googleapis.com'
SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']
FCM_ENDPOINT = f'v1/projects/{PROJECT_ID}/messages:send'
FCM_URL = f'{BASE_URL}/{FCM_ENDPOINT}'

# ==== apn token pushkit ios token-based voip p8 ===
TEAM_ID = "X4KBX2XY3B"
KEY_ID = "73BLP4N3WJ"
BUNDLE_ID = "com.habibApp.habib"
P8_PATH = ""
APNS_HOST = "https://api.push.apple.com" 



def _get_access_token():
  """Retrieve a valid access token that can be used to authorize requests.

  :return: Access token.
  """
  credentials = service_account.Credentials.from_service_account_info(
    data,   scopes=SCOPES)
  request = google.auth.transport.requests.Request()
  credentials.refresh(request)
  return credentials.token


cred = credentials.Certificate(data)
firebase_admin.initialize_app(cred)

async def send_silent_notification(device_token: str, data: dict = None, platform: str = 'android') -> dict:
    print(f'==(send-notif)-- platform: {platform}')

    access_token = _get_access_token()
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
    }

    payload = {
        'message': {
            'token': device_token,
            'data': {k: str(v) for k, v in (data or {}).items()},
        }
    }

    # Add platform-specific configuration
    if platform.lower() == 'ios':
        # iOS-specific configuration using APNS
        payload['message']['apns'] = {
            'headers': {
                'apns-priority': '5',  # Background notification priority
            },
            'payload': {
                'aps': {
                    'content-available': 1,  # Silent notification for iOS
                }
            }
        }
    else:
        # Android configuration (default behavior)
        payload['message']['android'] = {
            'priority': 'high',
        }

    response = requests.post(FCM_URL, headers=headers, json=payload)
    if response.status_code == 200:
        logger.info(f'-->Successfully sent Silent Notif for {platform}')
        return True
    else:
        logger.error(f'--> {response.json()}')
    return False



async def send_notification(ids: list, title: str = None, body: str = None, data=None,
                            extra_notification_kwargs: dict = None, platform: str = 'android') -> list:
    if not ids:
        return []

    chunked_ids = [ids[i:i + 500] for i in range(0, len(ids), 500)]

    responses = []
    for chunk in chunked_ids:

        access_token = _get_access_token()
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
        }

        payload = {
            'message': {
                'token': chunk[0],
                'notification': {
                    'title': title,
                    'body': body,
                },
                'data': {k: str(v) for k, v in (data or {}).items()},
            }
        }

        # Add platform-specific configuration
        if platform.lower() == 'ios':
            # iOS-specific configuration using APNS
            payload['message']['apns'] = {
                'headers': {
                    'apns-priority': '10',  # High priority for alert notifications
                },
                'payload': {
                    'aps': {
                        'alert': {
                            'title': title,
                            'body': body,
                        },
                        'sound': 'default',
                        'badge': 0,
                    }
                }
            }
        else:
            # Android configuration (default behavior)
            payload['message']['android'] = {
                'priority': 'high',
                'notification': {
                    'title': title,
                    'body': body,
                    # 'sound': 'incoming_call_sound',
                    'color': '#06EEBD',
                    # 'channel_id': 'incoming_call_channel',
                    'visibility': 'public',
                },
            }

        # Send the POST request to FCM API
        print(f'=========(send-notif)===****** platform: {platform}')
        response = requests.post(FCM_URL, headers=headers, json=payload)
        if response.status_code == 200:
            logger.warning(f'Successfully sent message for {platform}:', response.json())
            responses.append(response.json())
        else:
            responses.append({'status': 'error', 'message': ""})
            logger.error(f'Failed to send message notif for {platform}')

    return responses


    # android_notification_kwargs = extra_notification_kwargs or {}

        # multicast_message = messaging.MulticastMessage(
        #     notification=messaging.Notification(
        #         title=title,
        #         body=body,
        #     ),
        #     android=messaging.AndroidConfig(
        #         ttl=datetime.timedelta(seconds=20),
        #         priority='high',
        #         notification=messaging.AndroidNotification(
        #             title=title,
        #             body=body,
        #             sound='incoming_call_sound',
        #             vibrate_timings_millis=[0, 1000, 500, 1000],
        #             # **android_notification_kwargs,
        #             color='#06EEBD',
        #             channel_id='incoming_call_channel',  
        #             visibility='public', 
        #         ),
        #     ),
        #     data={k: str(v) for k, v in (data or {}).items()},
        #     tokens=chunk,
        # )

        # try:
            # response = messaging.send_multicast(multicast_message)
            # logging.info(f'{response.success_count} messages were sent successfully on attempt {attempt + 1}.')
            # logging.warning(f'{response.failure_count} messages failed on attempt {attempt + 1}.')
    
            # response_data = {
            #     'success_count': response.success_count,
            #     'failure_count': response.failure_count,
            #     'responses': [
            #         {
            #             'success': resp.success,
            #             'message_id': resp.message_id if resp.success else None,
            #             'exception': str(resp.exception) if not resp.success else None
            #         }
            #         for resp in response.responses
            #     ]
            # }
    
            # logging.info(json.dumps(response_data, ensure_ascii=False))
            # responses.append(response_data)
            # if response.success_count > 0:
                # break
                    
        # except messaging.FirebaseError as e:
            # logging.error(f'Error sending message: {e}')
            # responses.append({'status': 'error', 'message': str(e)})
            
            # await asyncio.sleep(5)

    # return responses
