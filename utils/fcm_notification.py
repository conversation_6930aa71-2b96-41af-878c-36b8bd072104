import time
import asyncio
import datetime
import logging
import json
import firebase_admin
from firebase_admin import credentials, messaging
from aiohttp import ClientSession
import requests
import argparse
from pyfcm import FCMNotification
from pyfcm.errors import FCMServerError
import firebase_admin
from firebase_admin import credentials, messaging
from google.oauth2 import service_account
import google.auth.transport.requests
import time, json, httpx, jwt
from pathlib import Path

# تنظیمات لاگ‌گذاری
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# تنظیمات Firebase
data = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

PROJECT_ID = 'habib-73dad'
BASE_URL = 'https://fcm.googleapis.com'
SCOPES = ['https://www.googleapis.com/auth/firebase.messaging']
FCM_ENDPOINT = f'v1/projects/{PROJECT_ID}/messages:send'
FCM_URL = f'{BASE_URL}/{FCM_ENDPOINT}'

# ==== apn token pushkit ios token-based voip p8 ===
TEAM_ID = "X4KBX2XY3B"
KEY_ID = "73BLP4N3WJ"
BUNDLE_ID = "com.habibApp.habib"
P8_PATH = "AuthKey_73BLP4N3WJ.p8"
APNS_HOST = "https://api.push.apple.com"



def _get_access_token():
  """Retrieve a valid access token that can be used to authorize requests.

  :return: Access token.
  """
  credentials = service_account.Credentials.from_service_account_info(
    data,   scopes=SCOPES)
  request = google.auth.transport.requests.Request()
  credentials.refresh(request)
  return credentials.token


cred = credentials.Certificate(data)
firebase_admin.initialize_app(cred)

async def send_silent_notification(device_token: str, data: dict = None, platform: str = 'android') -> dict:
    print(f'==(send-notif)-- platform: {platform}')

    access_token = _get_access_token()
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
    }

    payload = {
        'message': {
            'token': device_token,
            'data': {k: str(v) for k, v in (data or {}).items()},
        }
    }

    # Add platform-specific configuration
    if platform.lower() == 'ios':
        # iOS-specific configuration using APNS
        payload['message']['apns'] = {
            'headers': {
                'apns-priority': '5',  # Background notification priority
            },
            'payload': {
                'aps': {
                    'content-available': 1,  # Silent notification for iOS
                }
            }
        }
    else:
        # Android configuration (default behavior)
        payload['message']['android'] = {
            'priority': 'high',
        }

    response = requests.post(FCM_URL, headers=headers, json=payload)
    if response.status_code == 200:
        logger.info(f'-->Successfully sent Silent Notif for {platform}')
        return True
    else:
        logger.error(f'--> {response.json()}')
    return False



async def send_notification(ids: list, title: str = None, body: str = None, data=None,
                            extra_notification_kwargs: dict = None, platform: str = 'android') -> list:
    if not ids:
        return []

    chunked_ids = [ids[i:i + 500] for i in range(0, len(ids), 500)]

    responses = []
    for chunk in chunked_ids:

        access_token = _get_access_token()
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
        }

        payload = {
            'message': {
                'token': chunk[0],
                'notification': {
                    'title': title,
                    'body': body,
                },
                'data': {k: str(v) for k, v in (data or {}).items()},
            }
        }

        # Add platform-specific configuration
        if platform.lower() == 'ios':
            # iOS-specific configuration using APNS
            payload['message']['apns'] = {
                'headers': {
                    'apns-priority': '10',  # High priority for alert notifications
                },
                'payload': {
                    'aps': {
                        'alert': {
                            'title': title,
                            'body': body,
                        },
                        'sound': 'default',
                        'badge': 0,
                    }
                }
            }
        else:
            # Android configuration (default behavior)
            payload['message']['android'] = {
                'priority': 'high',
                'notification': {
                    'title': title,
                    'body': body,
                    # 'sound': 'incoming_call_sound',
                    'color': '#06EEBD',
                    # 'channel_id': 'incoming_call_channel',
                    'visibility': 'public',
                },
            }

        # Send the POST request to FCM API
        print(f'=========(send-notif)===****** platform: {platform}')
        response = requests.post(FCM_URL, headers=headers, json=payload)
        if response.status_code == 200:
            logger.warning(f'Successfully sent message for {platform}:', response.json())
            responses.append(response.json())
        else:
            responses.append({'status': 'error', 'message': ""})
            logger.error(f'Failed to send message notif for {platform}')

    return responses


async def send_voip_notification(apn_token: str, call_data: dict = None) -> dict:
    """
    ارسال نوتیفیکیشن VoIP برای iOS با استفاده از token-based authentication (P8)

    Args:
        apn_token: APN token برای دستگاه iOS
        call_data: اطلاعات تماس شامل call_id، caller_name، etc.

    Returns:
        dict: نتیجه ارسال نوتیفیکیشن
    """
    try:
        # ====== ساخت JWT برای Authorization ======
        p8_private_key = Path(P8_PATH).read_text()
        now = int(time.time())
        jwt_token = jwt.encode(
            {"iss": TEAM_ID, "iat": now},
            p8_private_key,
            algorithm="ES256",
            headers={"kid": KEY_ID}
        )

        # ====== هدرها و payload ======
        headers = {
            "authorization": f"bearer {jwt_token}",
            "apns-topic": f"{BUNDLE_ID}.voip",
            "apns-push-type": "voip",
            "apns-priority": "10",  # تحویل فوری برای تماس
        }

        # استخراج اطلاعات کامل تماس
        call_id = call_data.get('call_id', 'unknown')
        caller_name = call_data.get('caller_name') or call_data.get('from_user_fullname', 'Unknown Caller')
        call_type = call_data.get('call_type', 'voice')
        caller_avatar = call_data.get('caller_avatar') or call_data.get('from_user_avatar')
        has_video = call_data.get('has_video', call_type == 'video')
        room_name = call_data.get('room_name', f"{call_id}:Talk")
        incoming_call_uuid = call_data.get('incoming_call_uuid', str(call_id))

        payload = {
            "aps": {
                # برای بیدار کردن و گزارش CallKit
                "alert": {
                    "title": "Incoming call" if call_type == 'voice' else "Incoming video call",
                    "body": f"{caller_name} is calling…"
                },
                "sound": "default",
                "badge": 1
            },
            # اطلاعات کامل برای CallKit و مدیریت تماس
            "call_id": str(call_id),
            "caller_name": caller_name,
            "caller_id": call_data.get('caller_id'),
            "caller_avatar": caller_avatar,
            "call_type": call_type,
            "has_video": has_video,
            "room_name": room_name,
            "incoming_call_uuid": incoming_call_uuid,
            "notification_type": "incoming_call",
            "session_duration": call_data.get('session_duration'),
            "cost": call_data.get('cost'),
            "consultant_id": call_data.get('consultant_id'),
            "consultant_username": call_data.get('consultant_username'),
            "start_time": call_data.get('start_time'),
            "call_status": call_data.get('call_status', 'unconfirmed'),
            # اطلاعات اضافی برای app
            "act": call_data.get('act', 'ExternalNotifications'),
            "notif_type": call_data.get('notif_type', 'call'),
            "message_id": call_data.get('message_id', call_id)
        }

        # ====== ارسال ======
        url = f"{APNS_HOST}/3/device/{apn_token}"

        with httpx.Client(http2=True, timeout=10) as client:
            resp = client.post(url, headers=headers, content=json.dumps(payload).encode("utf-8"))

            logger.info(f'VoIP notification sent - Status: {resp.status_code}, APNS-ID: {resp.headers.get("apns-id")}, Response: {resp.text}')

            if resp.status_code == 200:
                logger.info(f'Successfully sent VoIP notification to iOS device')
                return {
                    'success': True,
                    'status_code': resp.status_code,
                    'apns_id': resp.headers.get("apns-id"),
                    'response': resp.text
                }
            else:
                logger.error(f'Failed to send VoIP notification - Status: {resp.status_code}, Response: {resp.text}')
                return {
                    'success': False,
                    'status_code': resp.status_code,
                    'error': resp.text
                }

    except Exception as e:
        logger.error(f'Error sending VoIP notification: {e}')
        return {
            'success': False,
            'error': str(e)
        }


    # android_notification_kwargs = extra_notification_kwargs or {}

        # multicast_message = messaging.MulticastMessage(
        #     notification=messaging.Notification(
        #         title=title,
        #         body=body,
        #     ),
        #     android=messaging.AndroidConfig(
        #         ttl=datetime.timedelta(seconds=20),
        #         priority='high',
        #         notification=messaging.AndroidNotification(
        #             title=title,
        #             body=body,
        #             sound='incoming_call_sound',
        #             vibrate_timings_millis=[0, 1000, 500, 1000],
        #             # **android_notification_kwargs,
        #             color='#06EEBD',
        #             channel_id='incoming_call_channel',  
        #             visibility='public', 
        #         ),
        #     ),
        #     data={k: str(v) for k, v in (data or {}).items()},
        #     tokens=chunk,
        # )

        # try:
            # response = messaging.send_multicast(multicast_message)
            # logging.info(f'{response.success_count} messages were sent successfully on attempt {attempt + 1}.')
            # logging.warning(f'{response.failure_count} messages failed on attempt {attempt + 1}.')
    
            # response_data = {
            #     'success_count': response.success_count,
            #     'failure_count': response.failure_count,
            #     'responses': [
            #         {
            #             'success': resp.success,
            #             'message_id': resp.message_id if resp.success else None,
            #             'exception': str(resp.exception) if not resp.success else None
            #         }
            #         for resp in response.responses
            #     ]
            # }
    
            # logging.info(json.dumps(response_data, ensure_ascii=False))
            # responses.append(response_data)
            # if response.success_count > 0:
                # break
                    
        # except messaging.FirebaseError as e:
            # logging.error(f'Error sending message: {e}')
            # responses.append({'status': 'error', 'message': str(e)})
            
            # await asyncio.sleep(5)

    # return responses
