import datetime
import enum

import pytz
from sqlalchemy import <PERSON>olean, Column, Integer, String, DateTime, JSON, ForeignKey, Table, Enum, func
from sqlalchemy.orm import validates, relationship
from fastapi_sqlalchemy import db

from models import Base
from models.rate import Rate
from models.topic import Topic

from utils.helpers import generate_token
from utils import get_languages

consultant_categories_pivot = Table(
    'consultant_categories',
    Base.metadata,
    Column('consultant_id', Integer, ForeignKey('consultants.id')),
    Column('category_id', Integer, ForeignKey('categories.id'))
)


class Consultant(Base):
    __tablename__ = "consultants"

    GenderChoices = (
        ('male', 'male'),
        ('female', 'female'),
        ('both', 'both'),
    )

    StatusChoices = (
        ('busy', 'busy'),
        ('online', 'online'),
        ('offline', 'offline'),
    )

    ContactTypeChoices = (
        ('chat', 'chat'),
        ('voice', 'voice'),
        ('video', 'video'),
    )
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    username = Column(String, unique=True, index=True)
    password = Column(String)
    topics = Column(String, nullable=True, index=True)
    languages = Column(String, nullable=True, index=True)
    call_languages = Column(String, nullable=True)
    device_os = Column(String, nullable=True) 

    contact_type = Column(String, nullable=True, )
    description = Column(String, nullable=True, )
    visible = Column(Boolean, default=True)
    is_banned = Column(Boolean, default=False)
    gender = Column(String, default='male')
    token = Column(String, default=generate_token, unique=True, index=True)
    fullname = Column(String, nullable=True)
    is_supporter = Column(Boolean, default=False)
    consultant_type = Column(String, default='regular')
    email = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    country = Column(String, nullable=True)
    state = Column(String, nullable=True)
    city = Column(String, nullable=True)
    address = Column(String, nullable=True)
    bio = Column(String, nullable=True)
    slogan = Column(String, nullable=True)

    expertise_field = Column(String, nullable=True)
    expertise = Column(String, nullable=True)

    is_ai = Column(Boolean, default=False)
    ai_project = Column(String, default="")
    ai_prompt = Column(String, default="")

    banned_reason = Column(String, default=None, )
    banned_at = Column(DateTime, default=None, )

    avatar_url = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now)

    description_translation = Column(JSON, default=dict) # [{"language_code": "en", "text": "description en"}, {"language_code": "ar", "text": "description ar"}]
    name_translation = Column(JSON, default=dict) # [{"language_code": "en", "text": "name en"}, {"language_code": "ar", "text": "name ar"}]
    topics_translation = Column(JSON, default=dict)# [{"language_code": "en", "text": "topics en"}, {"language_code": "ar", "text": "topics ar"}]
    first_messages = Column(JSON, default=dict)# [{"language_code": "en", "text": "topics en"}, {"language_code": "ar", "text": "topics ar"}]
    status_from_schedule = Column(Boolean, default=False, nullable=False)

    timezone = Column(String, nullable=True)
    fcm = Column(String, nullable=True)
    apn_token = Column(String, nullable=True)
    status = Column(String, default='offline', nullable=False, index=True)
    categories = relationship('Category', secondary=consultant_categories_pivot)
    scheduling = Column(JSON, nullable=True)
    rates = relationship('Rate', backref='consultant_rate', lazy='joined')
    first_message = Column(String, nullable=True, default="")
    is_tester = Column(Boolean, default=False, nullable=False)

    session_duration = Column(Integer, nullable=True)
    video_call_cost = Column(Integer, nullable=True)
    voice_call_cost = Column(Integer, nullable=True)
    calls = relationship("Call", back_populates="consultant")
    order = Column(Integer, default=0, index=True)     
    # Free limits for AI consultants
    free_messages_per_period = Column(Integer, default=3)  # Number of messages users can send for free per period (default: 3 messages)
    free_limit_period = Column(String, default='daily')  # Period for free messages: 'daily', 'weekly', 'monthly'


    @validates('contact_type', 'topics', 'languages', 'call_languages')
    def validate_value(self, key, value):
        # Modify the value of the object
        if type(value) is list:
            return ",".join(value)

        return value

    def to_dict(self, lang='en'):        
        if not isinstance(self.scheduling, dict) or not self.scheduling:
            self.scheduling = {
                "saturday": [],
                "sunday": [],
                "monday": [],
                "tuesday": [],
                "wednesday": [],
                "thursday": [],
                "friday": []
            }
        fullname_translation = self.get_translation("name_translation", lang)
        slogan_translation = self.get_translation("topics_translation", lang)
        description_translation = self.get_translation("description_translation", lang)
        first_messages = self.get_translation("first_messages", lang)
        
        return {
            'username': self.username.split(':')[0] if ':' in str(self.username) else self.username,            
            "fullname": fullname_translation or self.fullname or None,
            "avatar_url": self.avatar_url,
            'is_consultant': True,
            'is_supporter': self.is_supporter,
            'is_banned': bool(self.banned_at),
            'slogan': slogan_translation or self.slogan or None,
            'topics': self.get_topics(lang) or [],
            'languages': self.languages.split(',') if self.languages else [],
            'call_languages': self.call_languages.split(',') if self.call_languages else [],
            'contact_type': self.contact_type.split(',') if self.contact_type else [],
            'scheduling': self.scheduling,
            'status': self.status,
            'first_message': first_messages or None,
            'bio': self.bio,
            'description': description_translation or self.description or None,
            'expertise_field': self.expertise_field,
            'expertise': self.expertise,
            'session_duration': self.session_duration,  # مدت زمان هر جلسه
            'video_call_cost': self.video_call_cost,  # هزینه هر جلسه تصویری
            'voice_call_cost': self.voice_call_cost,  # هزینه هر جلسه صوتی
        }

    def get_translation(self, field: str, language_code: str):
        translations = getattr(self, field, [])
        languages = get_languages()
        if not isinstance(translations, list):
            return None
        for translation in translations:
            if translation.get("language_code") == language_code:
                text = translation.get("text")
                if text and text.strip():  
                    return text
                
        if hasattr(self, 'languages') and self.languages:
            first_language = self.languages.split(',')[0].strip()  
            first_language_code = None
            for lang in languages:
                if lang["name"] == first_language:
                    first_language_code = lang["code"]
                    break
            if first_language_code:
                for translation in translations:
                    if translation.get("language_code") == first_language_code:
                        text = translation.get("text")
                        if text and text.strip():  
                            return text
        return None

    def get_topics(self, lang):
        topic_titles = []
        try:
            topic_ids = [int(tid) for tid in self.topics.split(',') if tid.strip().isdigit()]
            with db():
                topics = db.session.query(Topic).filter(Topic.id.in_(topic_ids)).all()
                topic_titles = [topic.get_title_for_language(lang) for topic in topics]
                return topic_titles
        except Exception as exp:
            return topic_titles

    def is_consultant_available(self, client_timezone, hour=None, day=None):
        if not self.status:
            return False

        consultant_timezone = self.timezone or 'UTC'
        available_hours = self.scheduling or {}

        # Parse input hour and day

        if not hour and not day:
            client_time = datetime.datetime.now()
            # hour = client_time.strftime('%H:%M')
            day = client_time.strftime('%A')
        else:
            client_time = datetime.datetime.strptime(f"{day} {hour}", "%A %H:%M")

        # Convert client time to consultant's timezone
        client_time = pytz.timezone(client_timezone).localize(client_time)
        consultant_time = client_time.astimezone(pytz.timezone(consultant_timezone))

        # Check if the consultant is available at the given time
        for available_window in available_hours.get(day.lower(), []):
            start, end = available_window.split('-')
            start_time = datetime.datetime.strptime(start, "%H:%M").time()
            end_time = datetime.datetime.strptime(end, "%H:%M").time()

            if start_time <= consultant_time.time() <= end_time:
                return True

        return False

    def __str__(self):
        return self.username

    def calculate_avg_rate(self, session):
        avg_rate = session.query(func.avg(Rate.rate)).filter(Rate.consultant == self.id).scalar()
        return avg_rate if avg_rate is not None else 0
    
        
    def set_free_limits(self, free_messages_per_period=3, free_limit_period='daily'):
        """
        Set free limits for this consultant
        
        Args:
            free_messages_per_period: Number of messages users can send for free per period (default: 3)
            free_limit_period: Period for free messages: 'daily', 'weekly', 'monthly' (default: 'daily')
            
        Returns:
            bool: True if limits were set successfully
        """
        if not self.is_ai:
            return False  # Only AI consultants can have free limits
            
        # Validate period
        if free_limit_period not in ['daily', 'weekly', 'monthly']:
            free_limit_period = 'daily'  # Default to daily if invalid
            
        with db():
            self.free_messages_per_period = free_messages_per_period
            self.free_limit_period = free_limit_period
            db.session.commit()
            return True
            
    def get_free_limits(self):
        """
        Get free limits for this consultant
        
        Returns:
            dict: Dictionary with free limits information
        """
        return {
            'free_messages_per_period': self.free_messages_per_period,
            'free_limit_period': self.free_limit_period
        }