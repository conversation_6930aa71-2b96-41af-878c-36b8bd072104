import os
import secrets
from datetime import datetime, timedelta

import aiofiles
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, Request, Security, UploadFile, Response, HTTPException, status, Header
from fastapi import File as FastapiFile
from fastapi.security import API<PERSON>eyHeader
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi_sqlalchemy import DBSessionMiddleware
from fastapi_sqlalchemy import db
from slugify import slugify
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse
from schemas.admin import consultant as consultantDataModel
from livekit import api
from utils.save_models import get_or_save_user, fetch_user
from utils.helpers import sqlalchemy_obj_to_dict
from sqlalchemy import func, case

from apps import APIException, APIPermissionException
from apps.actions_v3 import ActionHandlerV3 
from apps.admin import router as admin_router
from apps.sfu import router as sfu_router
from apps.livekit_apis import router as livekit_apis_router
from apps.chat import app as chat_app
from apps.login import router as login_app
from models import SQLALCHEMY_DATABASE_URL, User, Consultant, Admin, Topic, Room, Report, SupportRequest, PushNotification, Rate, Call
from schemas.request_types import ReportUser, SupportRequestData, GetToken, RateConsultantRequest
from schemas.request_types_v2 import SendAudioMessageRequest, UserUpdateRequest
from schemas.response_types_v2 import ConsultantsResponse

load_dotenv(dotenv_path='./server.db.env')

templates = Jinja2Templates(directory="templates")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=['talk.habibapp.com', '127.0.0.1:8000', '127.0.0.1:3000', '127.0.0.1:8001', 'habibapp.com', '*'],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    DBSessionMiddleware,
    db_url=SQLALCHEMY_DATABASE_URL,
    engine_args={
        "pool_size": 10,         # تعداد اتصالات اصلی در استخر
        "max_overflow": 20,      # تعداد اتصالات اضافی قابل قبول
        "pool_timeout": 30,      # زمان انتظار برای اتصال
        "pool_recycle": 1800     # مدت زمان برای بازنشانی اتصالات (در ثانیه)
    }
)




async def get_current_user(token: str):
    if user := db.session.query(User).filter(User.token == token).first():
        return user

    if consultant := db.session.query(Consultant).filter(Consultant.token == token).first():
        return consultant

    if admin := db.session.query(Admin).filter(Admin.token == token).first():
        return admin


@app.middleware('http')
async def admin_middleware(request: Request, callback):
    if request.method == 'OPTIONS':
        return await callback(request)

    request.state.admin = None
    request.state.consultant = None

    if '/api/admin' in request.url.path:
        if token := request.headers.get('authorization'):
            token = token.replace('Token ', '')
            with db():
                print('ok')
                if user_auth := db.session.query(User).filter(User.token == token).first():
                    print('ok2')
                    consultant_username = user_auth.username.split(':')[0] 
                    if consultant := db.session.query(Consultant).filter(Consultant.username == consultant_username).first():
                        request.state.consultant = consultant
                        
                if consultant := db.session.query(Consultant).filter(Consultant.token == token).first():
                    request.state.consultant = consultant

                if admin := db.session.query(Admin).filter(Admin.token == token).first():
                    request.state.admin = admin

        if not request.state.admin and not request.state.consultant:
            return JSONResponse({"error": "Missing Or Invalid Token"}, status_code=401)

        # if "127.0.0.1" in request.url.hostname:
        #     if not request.state.admin or not request.state.consultant:
        #         with db():
        #             request.state.admin = db.session.query(Admin).first()
        #
        #         return await callback(request)

    return await callback(request)


import json
from livekit import api, rtc
from livekit.protocol import room as proto_room
from livekit.protocol import egress as proto_egress
from livekit.api.room_service import RoomService
from livekit.api.egress_service import EgressService
from apps.livekit_apis import LiveKitService
import logging
from requests.exceptions import Timeout, RequestException  
import requests
from utils.fcm_notification import send_notification, send_silent_notification, send_voip_notification

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')    

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            # Convert datetime to a string in ISO format
            return obj.isoformat()
        return super().default(obj)


@app.post("/livekit/token/",)
async def generate_token(data: GetToken):
    user = await get_current_user(data.user_token)
    call = db.session.query(Call).filter(Call.id == data.call_id).first()
    if not call:
        raise HTTPException(status_code=404, detail="Call not found")
    if call.status == "completed":
        raise HTTPException(status_code=403, detail="Call already completed")

    if call.client.id == user.id:
        request_data = {
            'act': 'ExternalNotifications',
            'notif_type': 'call',
            'call_type': call.call_type,
            'call_id': call.id,
            'call_status': call.status,
            'message_id': call.id,
            'from_user_fullname': call.client.fullname,
            'from_user_avatar': call.client.avatar_url,
            'from_user_username': call.client.username,
            'session_duration': call.consultant.session_duration,
            'notification_type': 'incoming_call'
        }     
        # Use consultant's device_os for platform-specific notifications
        # If device_os is not set, try to get platform from current user's connection
        consultant_platform = getattr(call.consultant, 'device_os', None)
        if not consultant_platform:
            # Fallback to android if no device_os is set
            consultant_platform = 'android'

        print(f"DEBUG: Consultant {call.consultant.username} device_os: {call.consultant.device_os}, using platform: {consultant_platform}")

        # اگر پلتفرم iOS است و apn_token موجود است، از VoIP استفاده کن
        if consultant_platform.lower() == 'ios' and hasattr(call.consultant, 'apn_token') and call.consultant.apn_token:
            print(f"DEBUG: Sending VoIP notification to iOS device with APN token")
            voip_result = await send_voip_notification(call.consultant.apn_token, request_data)
            print(f"DEBUG: VoIP notification result: {voip_result}")

            # همچنین FCM silent notification نیز ارسال کن (برای backup)
            await send_silent_notification(call.consultant.fcm, request_data, platform=consultant_platform)
        else:
            # برای Android یا iOS بدون APN token، از FCM استفاده کن
            await send_silent_notification(call.consultant.fcm, request_data, platform=consultant_platform)

    consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
    
    if consultant and consultant.session_duration != None: 
        session_duration_minutes = consultant.session_duration
        end_time = call.start_time + timedelta(minutes=session_duration_minutes)
    else:
        end_time = call.start_time + timedelta(minutes=10)
    
    call.end_time = end_time
    db.session.commit()
    call_metadata = {
        "call_id": call.id,
        "consultant_id": call.consultant_id,
        "client_id": call.client_id,
        "start_time": call.start_time,
        "end_time": end_time,
        "session_duration_minutes": consultant.session_duration if consultant else None,
        "timer_active": call.timer_active,
        "status": call.status
    }
    room_metadata = json.dumps(call_metadata, cls=CustomJSONEncoder)
    room_serice = LiveKitService()
    await room_serice.create_room(data.call_id, room_metadata)    
    
    call_id = f"{data.call_id}:Talk" 

    consultant = db.session.query(Consultant).filter(
        Consultant.user_id == user.id
    ).first()
    if consultant:
        metadata = consultant.to_dict()
        metadata['user_id'] = consultant.user_id
    else:
        metadata = user.to_dict()
    metadata['call_id'] = call.id
    metadata['call_status'] = call.status
    main_metadata = json.dumps(metadata, cls=CustomJSONEncoder)
                
    grant = api.VideoGrants(
        room_join=True,
        room=call_id,
        can_subscribe=True,
        can_publish=True,
        can_publish_data=True,
        can_update_own_metadata=True,
    )
    token = api.AccessToken(os.getenv('LIVEKIT_API_KEY'), os.getenv('LIVEKIT_API_SECRET')) \
        .with_identity(str(data.user_token)) \
        .with_name(str(data.user_token)) \
        .with_grants(grant) \
        .with_metadata(main_metadata)
    logging.info(f"Generated token for room: {call_id}/ type: {type(user)}")
    try:

        headers = {
            "Authorization": "Bearer f0e0d026be3fdd0d797fcea5fdb529fd1f435a544d22fa72d72a17ecd09b0267",
            "Content-Type": "application/json"
        }
        payload = {"ttl": 86400}

        response = requests.post(
            "https://rtc.live.cloudflare.com/v1/turn/keys/999cc9e770a824b5e3ae02f909926ef9/credentials/generate",
            headers=headers,
            json=payload,
            timeout=10
        )
        response.raise_for_status()
        cloudflare_response = response.json()
        
        if "iceServers" in cloudflare_response:
            ice_servers = cloudflare_response["iceServers"]
            if isinstance(ice_servers, dict) and "urls" in ice_servers:
                return JSONResponse({
                    "token": token.to_jwt(),
                    "iceServers": ice_servers
                }, status_code=status.HTTP_200_OK)
            else:
                logging.error("Invalid 'iceServers' structure in Cloudflare response")
                return JSONResponse({
                    "token": token.to_jwt(),
                    "iceServers": {}
                }, status_code=status.HTTP_200_OK)
        else:
            logging.error("'iceServers' key not found in Cloudflare response")
            return JSONResponse({
                "token": token.to_jwt(),
                "iceServers": {}
            }, status_code=status.HTTP_200_OK)

    except Timeout:
        logging.error("Timeout error while fetching TURN credentials from Cloudflare.")
        return JSONResponse({
            "token": token.to_jwt(),
            "iceServers": {}
        }, status_code=status.HTTP_200_OK)
            
    except Exception as e:
        logging.error(f"Error fetching TURN credentials from Cloudflare: {e}")
        return JSONResponse({
            "token": token.to_jwt(),
            "iceServers": {}
        }, status_code=status.HTTP_200_OK)



@app.get("/", include_in_schema=False)
async def get(request: Request):
    socket_protocol = 'wss' if ".com" in request.url.hostname else 'ws'
    consultants = db.session.query(Consultant).all()
    users = db.session.query(User).all()
    admins = db.session.query(Admin).all()

    return templates.TemplateResponse(
        "index.html", {
            'request': request,
            'host': request.url.netloc,
            'socket_protocol': socket_protocol,
            'consultants': consultants,
            'users': users,
            'admins': admins
        })


@app.get("/v2/", include_in_schema=False)
async def get(request: Request):
    socket_protocol = 'wss' if ".com" in request.url.hostname else 'ws'
    consultants = db.session.query(Consultant).all()
    users = db.session.query(User).all()
    admins = db.session.query(Admin).all()

    return templates.TemplateResponse(
        "index_v2.html", {
            'request': request,
            'host': request.url.netloc,
            'socket_protocol': socket_protocol,
            'consultants': consultants,
            'users': users,
            'admins': admins
        })


@app.get("/v3/", include_in_schema=False)
async def get(request: Request):
    socket_protocol = 'wss' if ".com" in request.url.hostname else 'ws'
    # consultants = db.session.query(Consultant).all()
    # users = db.session.query(User).all()
    # admins = db.session.query(Admin).all()

    return templates.TemplateResponse(
        "index_v3.html", {
            'request': request,
            'host': request.url.netloc,
            'socket_protocol': socket_protocol,
            # 'consultants': consultants,
            # 'users': users,
            # 'admins': admins
        })


@app.post("/uploadfile/")
async def create_file(file: UploadFile = None):
    if file is None:
        raise HTTPException(status_code=400, detail="No file provided")

    # Extract filename and extension properly
    original_filename, file_extension = os.path.splitext(file.filename)

    # Apply slugify only to the filename part, not the extension
    safe_filename = slugify(secrets.token_urlsafe(4) + '_' + original_filename)

    # Combine safe filename with original extension
    filename = safe_filename + file_extension

    os.makedirs('static/uploads', exist_ok=True)
    async with aiofiles.open(f'static/uploads/{filename}', 'wb') as out_file:
        content = await file.read()
        await out_file.write(content)

    return {"path": f'/static/uploads/{filename}'}

@app.get("/init/", name='Stats Summary')
async def get(request: Request):
    consultants = db.session.query(Consultant).all()

    return {
        "experts": consultants,
        "questions": db.session.query(Room).count(),
        "languages": 10,
        "subjects": db.session.query(Topic).count(),
    }


auth_header = APIKeyHeader(name='Authorization', scheme_name='secret-header')


@app.get("/consultants/list/", name='get consultants', response_model=consultantDataModel.ConsultantList)
def get_consultants_list(request: Request):
    qs = db.session.query(Consultant).order_by(Consultant.id.desc()).all()

    consultant_data = []
    for obj in qs:
        data = obj.to_dict()
        if data.get('is_supporter') is None:
            data['is_supporter'] = False  # اگر None است، False تنظیم شود
        consultant_data.append(data)

    js = consultantDataModel.ConsultantList(results=consultant_data).json()
        
    # js = consultantDataModel.ConsultantList(results=[obj.to_dict() for obj in qs]).json()

    return Response(js, media_type='application/json')

from math import ceil
from sqlalchemy import case, func

@app.get("/consultants/{username}/", response_model=ConsultantsResponse)
def get_consultant(username: str, lang: str = Header("en", alias="Accept-Language")):
    consultant = db.session.query(Consultant).filter_by(username=username).first()
    if not consultant:
        return {}, 404

    # دریافت آیدی‌های تاپیک‌ها
    topic_ids = [int(tid) for tid in consultant.topics.split(',')] if consultant.topics else []
    
    # جستجوی عناوین تاپیک‌ها بر اساس زبان
    topics = []
    if topic_ids:
        db_topics = db.session.query(Topic).filter(Topic.id.in_(topic_ids)).all()
        for t in db_topics:
            topics.append(t.get_title_for_language(lang))
    
    # تبدیل شیء به دیکشنری و جایگزینی topics
    obj_dict = consultant.to_dict(lang)
    
    # محاسبه تعداد پیام‌های خوانده نشده
    unread_count = 0
    
    # محاسبه میانگین امتیازات
    rate_data = (
        db.session.query(
            Rate.consultant,
            func.avg(Rate.rate).label('avg_rate')
        )
        .filter(Rate.consultant == consultant.id)
        .group_by(Rate.consultant)
        .first()
    )
    
    avg_rate = ceil(rate_data[1] if rate_data else 5.0)
    
    # بررسی آیا مشاور در تماس دیگری است
    is_on_another_call = db.session.query(Call).filter(
        Call.consultant_id == consultant.id,
        Call.status == "confirmed",
        Call.end_time.is_(None)  # بررسی تماس‌های باز
    ).order_by(Call.id.desc()).first() is not None
    
    # ایجاد پاسخ با مدل ConsultantsResponse
    return ConsultantsResponse(
        act="consultant",
        username=obj_dict.get('username'),
        fullname=obj_dict.get('fullname'),
        slogan=obj_dict.get('slogan'),
        topics=topics,
        languages=obj_dict.get('languages'),
        call_languages=obj_dict.get('call_languages'),
        avatar_url=obj_dict.get('avatar_url'),
        estimate_time="",
        status=obj_dict.get('status', 'offline'),
        is_on_another_call=is_on_another_call,
        contact_type=obj_dict.get('contact_type'),
        bio=obj_dict.get('description'),
        description=obj_dict.get('description'),
        first_message=obj_dict.get('first_message'),
        scheduling=obj_dict.get('scheduling', {}),
        categories=[c.title for c in consultant.categories] if hasattr(consultant, 'categories') and consultant.categories else [],
        unread_count=unread_count,
        avg_rate=avg_rate,
        session_duration=obj_dict.get('session_duration'),
        video_call_cost=obj_dict.get('video_call_cost'),
        voice_call_cost=obj_dict.get('voice_call_cost'),
        is_ai=obj_dict.get('is_ai', False),
        ai_project=obj_dict.get('ai_project')
    )
    

@app.put("/update-user/")
async def update_user(request: Request, user_data: UserUpdateRequest):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException
    
    user = get_or_save_user(token, db)    
    if user_data.age is not None:
        user.age = user_data.age
    if user_data.preferred_languages is not None:
        user.preferred_languages = ", ".join(user_data.preferred_languages) if user_data.preferred_languages else None
    if user_data.gender is not None:
        user.gender = user_data.gender
    db.session.commit()
    db.session.refresh(user)
    return user.to_dict()

@app.post("/consultant/rate")
async def rate_consultant(request: Request, data: RateConsultantRequest):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException
    
    user = get_or_save_user(token, db)
    username = user.username.split(':')[0]
    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token {token} not found'
        }, 400)
    
    consultant = db.session.query(Consultant).filter(Consultant.username == data.consultant).first()
    if not consultant:
        return JSONResponse({
            'success': False,
            'result': f'Consultant not found'
        }, 400)

        raise HTTPException(status_code=404, detail="Consultant not found")
    
    new_rate = Rate(
        user=int(user.id),
        consultant=int(consultant.id),
        rate=int(data.rate),
        created_at=datetime.now()
    )
    
    db.session.add(new_rate)
    db.session.commit() 
    return JSONResponse({'success': True, 'result': 'ok'})

@app.get("/me/", name='get current user')
async def get_me(request: Request, header_value1=Security(auth_header)):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException

    user = await get_current_user(token)
    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token {token} not found'
        }, 400)

    user_dict = user.to_dict()
    if type(user) is Admin:
        role = 'admin'
    elif type(user) is Consultant:
        role = 'consultant'
    else:
        role = 'client'

    user_dict['role'] = role
    return JSONResponse(user_dict)


@app.get("/notif/read/")
async def mark_all_notifications_read(request: Request):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException
    
    user = get_or_save_user(token, db)
    username = user.username.split(':')[0]
    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token {token} not found'
        }, 400)

    count = db.session.query(PushNotification).count()
    db.session.query(PushNotification).filter(PushNotification.username == username).update({"is_read": True})
    db.session.commit()  # ذخیره تغییرات در دیتابیس
   
    return JSONResponse({
        'success': True,
        'result': 'All notifications marked as read'
    }, 200)    
    
    
@app.get("/notif/")
async def list_notifications(request: Request):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException


    print(f'notif>> {token}')
    user = get_or_save_user(token, db)

    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token not found'
        }, 400)
    username = user.username.split(':')[0]

    count = db.session.query(PushNotification).count()
    notifications = db.session.query(PushNotification).filter(PushNotification.username == username)
    results = [
        {
            "title": n.title,
            "message": n.message,
            "data": n.data,
            "result": n.result,
            "is_read": n.is_read,
            "created_at": n.created_at.isoformat() if n.created_at else None,
            "updated_at": n.updated_at.isoformat() if n.updated_at else None,
        }
        for n in notifications
    ]
    return {
        "count": count,
        "next": None,  
        "previous": None,  
        "results": results
    }    



@app.post("/report/")
async def report(request: Request, data: ReportUser):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException

    user = await get_current_user(token)
    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token {token} not found'
        }, 400)

    reported_user = db.session.query(User).filter(User.username == data.username).first() or db.session.query(
        Consultant).filter(
        Consultant.username == data.username
    ).first()

    if not reported_user:
        return JSONResponse({
            'success': False,
            'result': f'User with username {data.username} not found'
        }, 400)

    obj = Report(
        reporter_id=user.id,
        reporter_type=user.__class__.__name__,
        user_id=reported_user.id,
        user_type=reported_user.__class__.__name__,
        username=data.username,
        message=data.reason,
        reporter_username=user.username,
        reporter_fullname=user.fullname,
        fullname=reported_user.fullname
    )
    db.session.add(obj)
    db.session.commit()

    return JSONResponse({
        "success": True,
        "result": "report submitted",
    })


@app.get('/report/subjects/')
async def report_subjects(request: Request):
    return JSONResponse({
        'result': [
            "abuse", "inappropriate-content", "harassment", "suicide",
            "hate-speech", "violence", "self-harm", "nudity", "gore",
            "obscenity", "indecency", "offensive-language",
            "discrimination", "bullying", "cyberbullying",
        ]
    })


@app.get('/support/subjects/')
async def support_subjects(request: Request):
    return JSONResponse({
        'result': [
            "donate", "collaborate", "report a bug", "request a feature",
            "provide feedback", "suggest an idea", "ask a question",
            "ask for a referral", "request a review", "ask for a testimonial",
            "request a case study",
        ]
    })


@app.post("/support/")
async def support_request(request: Request, data: SupportRequestData):
    token = request.headers.get('authorization', '')
    token = token.replace('Token ', '')
    if not token:
        raise APIPermissionException

    user = await get_current_user(token)
    if not user:
        return JSONResponse({
            'success': False,
            'result': f'User with token {token} not found'
        }, 400)

    obj = SupportRequest(
        description=data.description,
        user_id=user.id,
        subject=data.subject,
        wa_number=data.wa_number,
    )
    db.session.add(obj)
    db.session.commit()

    return JSONResponse({
        "success": True,
        "result": "support request submitted",
    })


# @app.post('stream/callback/loggedin/')
# def stream_handler_loggedin(request: Request):
#     return JSONResponse({
#         'status': 'ok'
#     })


# @app.post("/change-password/")
# async def change_password(request: Request, data: Login):
#     token = request.headers.get('authorization', '')
#     token = token.replace('Token ', '')
#     if not token:
#         return JSONResponse({
#             "success": False,
#             "result": "missing authorization token",
#         }, status_code=400)
#
#     user = await get_current_user(token)
#     if not user:
#         return JSONResponse({
#             'success': False,
#             'result': f'User with token {token} not found'
#         }, 400)
#
#     user = Admin.objects(username=data.username).first() or Consultant.objects(username=data.username).first()
#     if not user:
#         return JSONResponse({
#             'success': False,
#             'result': f'User with username {data.username} not found'
#         }, 400)
#
#     user.password = get_password_hash(data.password)
#     user.save()
#
#     return JSONResponse({
#         "success": True,
#         "result": "password changed successfully",
#     })
#

@app.get("/health/websocket", name='websocket_health_check')
async def websocket_health_check():
    """
    WebSocket uptime monitoring endpoint for Kuma.

    This endpoint performs a health check of the WebSocket connection by:
    1. Connecting to the WebSocket server
    2. Sending a getConsultants request
    3. Validating the response schema
    4. Returning HTTP 200 for success or HTTP 500 for failure

    Returns:
        HTTP 200: WebSocket is healthy and consultant list retrieval works
        HTTP 500: WebSocket is unhealthy or consultant list retrieval failed
    """
    import logging
    from utils.websocket_client import perform_websocket_health_check
    logger.info(f'---===websocket_health_check===---')
    # Temporarily disable debug and info logs for health check
    # Get all relevant loggers
    main_logger = logging.getLogger(__name__)
    websocket_logger = logging.getLogger('utils.websocket_client')
    websockets_lib_logger = logging.getLogger('websockets')

    # Store original log levels
    original_levels = {
        'main': main_logger.level,
        'websocket': websocket_logger.level,
        'websockets_lib': websockets_lib_logger.level
    }

    try:
        # Set log levels to WARNING and above (suppress DEBUG and INFO)
        main_logger.setLevel(logging.WARNING)
        websocket_logger.setLevel(logging.WARNING)
        websockets_lib_logger.setLevel(logging.WARNING)

        # Perform the health check with explicit configuration
        result = await perform_websocket_health_check(
            websocket_url="wss://qa.habibapp.com/ws/",
            auth_token="516e059d2a6b31d74b6a9e4c8f98fe4e8413efbc",
            timeout=500
        )

        if result['status'] == 'success':
            return JSONResponse(
                status_code=200,
                content={
                    "status": "healthy",
                    "message": "WebSocket consultant list retrieval successful",
                    "details": {
                        "consultant_count": result['consultant_count'],
                        "response_time_ms": result['response_time_ms'],
                        "connected": result['connected'],
                        "response_valid": result['response_valid']
                    }
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "status": "unhealthy",
                    "message": "WebSocket consultant list retrieval failed",
                    "error": result['error'],
                    "details": {
                        "connected": result['connected'],
                        "request_sent": result['request_sent'],
                        "response_received": result['response_received'],
                        "response_valid": result['response_valid']
                    }
                }
            )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "message": "Unexpected error during WebSocket health check",
                "error": str(e)
            }
        )
    finally:
        # Restore original log levels
        main_logger.setLevel(original_levels['main'])
        websocket_logger.setLevel(original_levels['websocket'])
        websockets_lib_logger.setLevel(original_levels['websockets_lib'])



@app.exception_handler(APIException)
async def api_exception_handler(request: Request, exc: APIException):
    return JSONResponse(
        status_code=400,
        content=APIException.detail,
    )


@app.exception_handler(APIPermissionException)
async def permission_exception_handler(request: Request, exc: APIPermissionException):
    return JSONResponse(
        status_code=403,
        content={
            'error': APIPermissionException.detail,
        },
    )

@app.post("/upload_audio/")
async def upload_audio(file: UploadFile = FastapiFile(...)):
    # Extract filename and extension properly
    original_filename, file_extension = os.path.splitext(file.filename)

    # Apply slugify only to the filename part, not the extension
    safe_filename = slugify(secrets.token_urlsafe(4) + '_' + original_filename)

    # Combine safe filename with original extension
    filename = safe_filename + file_extension

    os.makedirs('static/uploads/audio', exist_ok=True)
    file_path = f'static/uploads/audio/{filename}'
    async with aiofiles.open(file_path, 'wb') as out_file:
        content = await file.read()
        await out_file.write(content)
    return {"audio_url": f'/static/uploads/audio/{filename}'}


@app.post("/send_audio_message/")
async def send_audio_message(request: Request, data: SendAudioMessageRequest):
    action_handler = ActionHandlerV3()
    action_handler.from_user = await get_current_user(request.headers.get('Authorization'))
    return await action_handler.send_audio_message(data)


app.mount("/static", StaticFiles(directory="static"), name="static")

app.include_router(admin_router)
app.include_router(login_app)
app.include_router(sfu_router)
app.include_router(livekit_apis_router)

# app.include_router(zego_event_handler)

app.mount("", chat_app, "websocket")

if __name__ == "__main__":
    uvicorn.run(
        'main:app', port=8001, host="127.0.0.1",
    )
