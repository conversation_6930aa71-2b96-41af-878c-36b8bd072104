import os
import json
import logging
import threading
import asyncio
import websocket
import time
import requests
from datetime import datetime, timedelta, timezone
from typing import List, Dict
from starlette.responses import HTMLResponse
from fastapi import APIRouter
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException, APIRouter, BackgroundTasks
from fastapi.responses import Response
from passlib.context import CryptContext
from sqlalchemy import func, case
from starlette.responses import JSONResponse
from livekit import api, rtc
from livekit.protocol import egress as proto_egress
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from fastapi_sqlalchemy import db
from apps import APIPermissionException
from utils.fcm_notification import send_silent_notification
from models import *
from schemas.admin import category as category_schema
from schemas.admin import consultant as consultantDataModel
from schemas.admin.admin import BanUser
from schemas.admin.report import ReportList
from schemas.request_types import UpdateRoom
from schemas.response_types import RoomsResponse
from apps.livekit_apis import LiveKitService
from utils.charts import consultant_activity_chart, activity_chart, consultant_activity_chart2
from utils.helpers import sqlalchemy_obj_to_dict
from urllib.parse import urlencode


templates = Jinja2Templates(directory="templates")
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


router = APIRouter(
    prefix="/sfu"
)


@router.get("/test", include_in_schema=False)
async def get(request: Request):

    return templates.TemplateResponse(
        "rtc.html", {
            'request': request,
            'host': request.url.netloc,
        })

room_participants = {}
egress_ids = {}

class CustomJSONEncoder(json.JSONEncoder):
    from datetime import datetime
    def default(self, obj):
        if isinstance(obj, datetime):
            # Convert datetime to a string in ISO format
            return obj.isoformat()
        return super().default(obj)


@router.post("/webhook")
async def webhook(request: Request, background_tasks: BackgroundTasks):
    from datetime import datetime, timedelta, timezone
    LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')
    LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')    
    token_verifier = api.TokenVerifier(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    webhook_receiver = api.WebhookReceiver(token_verifier)
    auth_token = request.headers.get("Authorization")
    if not auth_token:
        raise HTTPException(status_code=401, detail="Invalid signature")
    body = await request.body()
    event = webhook_receiver.receive(body.decode("utf-8"), auth_token)
    room_name = event.room.name
    logger.info(f'===============================')
    logger.info(f'---received hook--- >> {room_name}/--{event.event}')
    if not room_name.endswith(':Talk'):
        return {"status": "ok"}



    if event.event == "room_started":
        if room_name not in room_participants:
            room_participants[room_name] = []
        # livekit_service = LiveKitService()
        # egress_id = await livekit_service.egress_room_start(room_name)
        # egress_ids[str(room_name)] = egress_id

        # background_tasks.add_task(check_participants_after_delay, room_name)
        # asyncio.create_task(check_participants_after_delay(room_name))
        # Schedule the check for participants after a delay using APScheduler
    
    if event.event == "participant_joined":
        if room_name not in room_participants:
            room_participants[room_name] = []
        room_participants[room_name].append(str(event.participant.identity))
        print(f'--> participant_joined save => {room_participants}')
        if len(room_participants[room_name]) == 2:
            call_id = room_name.split(':')[0]
            call = db.session.query(Call).filter(Call.id == int(call_id)).first()
            call.start_time = datetime.now(timezone.utc)  # Fixed: Use UTC time consistently
            consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
            consultant.status = 'busy'

            session_duration_minutes = consultant.session_duration
            end_time = call.start_time + timedelta(minutes=session_duration_minutes)
            call.end_time = end_time
            
            call.timer_active = True
            call.status = 'confirmed'
            cost = consultant.video_call_cost if call.call_type == 'video' else consultant.voice_call_cost
            call.cost = cost
            db.session.commit()
            call_metadata = {
                "call_id": call.id,
                "consultant_id": call.consultant_id,
                "client_id": call.client_id,
                "start_time": call.start_time.isoformat() if call.start_time else None, # Key change: ISO format
                "end_time": end_time.isoformat() if end_time else None,
                "session_duration_minutes": consultant.session_duration if consultant else None,  # Fixed: consistent key name
                "timer_active": True,
                "status": call.status
            }
            room_metadata = json.dumps(call_metadata, default=str)
            livekit_service = LiveKitService()
            await livekit_service.update_metadata_room(call.id, room_metadata)
            # background_tasks.add_task(check_participants_after_, room_name)
            asyncio.create_task(deduct_coins_after_delay(room_name, call.id, cost, call.client, consultant))

            
    if event.event == "participant_left":
        if room_name in room_participants:
            if event.participant.identity in room_participants[room_name]:
                room_participants[room_name].remove(event.participant.identity)
                logger.info(f'-webhock-participant_left--deleted')
                call_id = room_name.split(':')[0]
                call = db.session.query(Call).filter(Call.id == int(call_id)).first()

                # تعیین وضعیت بر اساس timer_active
                if call.timer_active == True:
                    # تماس واقعاً شروع شده بود (هر دو participant وارد شده بودند)
                    call.status = "completed"
                    call.end_time = datetime.now(timezone.utc)
                    # تنظیم وضعیت مشاور به online
                    consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
                    consultant.status = 'online'
                # else:
                    # تماس هرگز شروع نشده بود (مشاور وارد نشده بود)
                    # status را تغییر نده - unconfirmed باقی می‌ماند
                logger.info(f'Call {call.id} never started (timer_active=False), keeping status as {call.status}')

                data = {
                    'act': 'ExternalCallCanceledUser',
                    'notif_type': 'callCanceled',
                    'call_type': call.call_type,
                    'call_id': call.id,
                    'call_status': call.status,
                    'message_id': call.id,
                    'from_user_fullname': call.client.fullname,
                    'from_user_avatar': call.client.avatar_url,
                    'from_user_username': call.client.username,
                    'session_duration': call.consultant.session_duration,
                    'message': 'call cancel user'
                }
                # Use consultant's device_os for platform-specific notifications
                consultant_platform = getattr(call.consultant, 'device_os', 'android')
                await send_silent_notification(call.consultant.fcm, data, platform=consultant_platform)

                db.session.commit()
                if not room_participants[room_name]:
                    del room_participants[room_name]
            else:
                logging.warning(f"Participant {event.participant.identity} not found in room {room_name}")
        else:
            logging.warning(f"Room {room_name} not found in room_participants")

        
    if event.event == "room_finished":
        if room_name in room_participants:
            del room_participants[room_name]
        call_id = room_name.split(':')[0]
        if call := db.session.query(Call).filter(Call.id == int(call_id)).first():
            # room_finished: فقط cleanup کن، status را تغییر نده
            # چون status قبلاً در participant_left یا close-room تنظیم شده

            # فقط اگر تماس واقعاً شروع شده بود، end_time را تنظیم کن
            if call.timer_active == True:
                call.end_time = datetime.now(timezone.utc)

                # تنظیم وضعیت مشاور به online
                consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
                consultant.status = 'online'

            # status را تغییر نده - قبلاً در participant_left یا close-room تنظیم شده
            db.session.commit()
            
            # egress_id = egress_ids.get(str(room_name))   
            # logger.warning(f'===egress_id=>{egress_id}')      
            # if egress_id:
            #     livekit_service = LiveKitService()
            #     egress_id = await livekit_service.egress_ended(egress_id)
            #     if str(room_name) in egress_ids:
            #         del egress_ids[str(room_name)]
     
    return {"status": "ok"}

import time

async def check_participants_after_delay(room_name: str):
    start_time = time.time()
    await asyncio.sleep(50)  
    logger.warning(f'---check_participants_after_delay-========================>{room_name}, room_participants: {room_participants}')
    if room_name in room_participants and len(room_participants[room_name]) < 2:
        livekit_service = LiveKitService()
        await livekit_service.delete_room(room_name)  
        logger.info(f"Room {room_name} deleted due to insufficient participants.")
    else:
        logger.info(f"Room {room_name} not deleted. Participants: {room_participants.get(room_name, [])}")



async def deduct_coins_after_delay(room_name: str, call_id: int, cost: float, client, consultant):
    # print(f'-deduct_coins_after_delay-> {room_name}// {call_id}//{cost}')
    await asyncio.sleep(80)  

    if room_name in room_participants and len(room_participants[room_name]) == 2:
        url = "https://habibapp.com/habcoin/pay/"
        username = client.username.split(':')[0] if ':' in client.username else client.username
        params = {
            "username": username,
            "amount": cost,
            "token": "t5yugymks5458fd4ghfg6h6fg",
            "service": "consultants",
            "object_id": consultant.id
        }

        logging.info("----event deduct_coins", params)

        encoded_params = urlencode(params)
        admin_token = os.environ.get('admin_token') or 'e9a236c586d4fb90f7f7ce2c70392d80069022d2'
        session = requests.Session()
        session.headers = {
            'Content-Type': 'application/json',
            'AUTHORIZATION': f'Token {admin_token}',
        }

        try:
            response = session.get(f"{url}?{encoded_params}", headers={
                'user-agent': 'dart:io'
            })
            response.raise_for_status()
            result = response.json()
            if result.get("status") == "Coins deducted successfully":
                logging.info("Payment successful")
                return True
            else:
                logging.error("Payment failed: Unexpected response")
                return False
        except requests.exceptions.RequestException as e:
            logging.error(f"Payment failed: {e}")
            return False
    else:
        logging.info("Payment not processed: Not enough participants in the room")
        return False