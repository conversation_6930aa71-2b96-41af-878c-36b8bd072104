import re
import os
import logging
import json
import aiohttp
from datetime import datetime, timedelta, timezone
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException, APIRouter, Body, status
from starlette.responses import HTMLResponse
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from livekit import api, rtc
from livekit.protocol import room as proto_room
from livekit.protocol import egress as proto_egress
from livekit.api.room_service import RoomService
from livekit.api.egress_service import EgressService
from utils.helpers import sqlalchemy_obj_to_dict
from fastapi_sqlalchemy import db
from utils.fcm_notification import send_silent_notification
from models import *


router = APIRouter(
    prefix="/livekit-apis"
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')    

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            # Convert datetime to a string in ISO format
            return obj.isoformat()
        return super().default(obj)


class LiveKitService:
    def __init__(self):
        self.server_url = "https://livekit.newhorizonco.uk"
        self.api_key = LIVEKIT_API_KEY
        self.api_secret = LIVEKIT_API_SECRET

    async def get_room_service(self):
        client_session = aiohttp.ClientSession()
        room_service = RoomService(
            client_session,
            self.server_url,
            self.api_key,
            self.api_secret
        )
        return room_service, client_session


    async def check_room_exists(self, room_name: str):
        """چک کردن وجود اتاق و ایجاد آن اگر وجود ندارد"""
        room_service, client_session = await self.get_room_service()

        try:
            # گرفتن لیست اتاق‌ها
            list_rooms_request = proto_room.ListRoomsRequest(names=[room_name])
            list_rooms_response = await room_service.list_rooms(list_rooms_request)

            # بررسی وجود اتاق
            room_exists = any(room.name == room_name for room in list_rooms_response.rooms)
            # if not room_exists
            if not room_exists:
                return False
            return True

        except Exception as e:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error managing room: {e}")
        finally:
            await client_session.close()


    async def create_room(self, room_name: str, metadata) -> bool:
        room_name = f"{room_name}:Talk"
        room_service, client_session = await self.get_room_service()
        try:

            room = proto_room.CreateRoomRequest(
                name=str(room_name),
                metadata=metadata
            )
            # async with aiohttp.ClientSession() as client_session:
            room_response = await room_service.create_room(room)
            logging.info(f"------========-room-created-========------")            

            return True
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")

        # finally:
            # await client_session.close()

    async def egress_room_start(self, room_name: str) -> bool:
        try:
            print(f'-call--egress_room_start--> {room_name}')
            egress_request = proto_egress.RoomCompositeEgressRequest(
                room_name=str(room_name),
                # layout="speaker",
                audio_only=True,
                # video_only=True,
                file_outputs=[proto_egress.EncodedFileOutput(
                file_type=proto_egress.EncodedFileType.OGG,
                    filepath=f"/output/mesbahi/{room_name}.ogg",

                )]      
            )
            async with aiohttp.ClientSession() as client_session:
                egress_service = EgressService(
                    client_session,
                    url=self.server_url,
                    api_key=self.api_key,
                    api_secret=self.api_secret
                )              
                egress_info = await egress_service.start_room_composite_egress(egress_request)
                logging.info(f"------========-egress_service-started-========------{egress_info.egress_id}")            

                return egress_info.egress_id 
        except Exception as e:
            logger.error(f'=========egress_room_start==>{e}')
            raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")

    async def egress_ended(self, egress_id: str) -> bool:
        try:
            print(f'-call--egress_ended--> {egress_id}')
            async with aiohttp.ClientSession() as session:
                egress_service = EgressService(
                    session,
                    url=self.server_url,
                    api_key=self.api_key,
                    api_secret=self.api_secret
                )  
            try:
                egress_info = await egress_service.get_egress_info(proto_egress.EgressInfoRequest(egress_id=egress_id))
                if egress_info.status != proto_egress.EgressStatus.EGRESS_COMPLETE:
                    stop_request = proto_egress.StopEgressRequest(egress_id=egress_id)
                    await egress_service.stop_egress(stop_request)
                    logger.info(f"Egress {egress_id} stopped successfully.")
            except Exception as e:
                # logger.error(f'-error-webhock-room-finished>> {e}')
                return None

        except Exception as e:
            logger.error(f'=========egress_ended==>{e}')
            raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")



    async def delete_room(self, room_name: str) -> bool:
        try:
            if not room_name.endswith(':Talk'):
                room_name = f"{room_name}:Talk"
            room_service, client_session = await self.get_room_service()  # فرض می‌کنیم که client_session قبلاً باز شده است
            room_request = proto_room.DeleteRoomRequest(room=room_name)
            room_response = await room_service.delete_room(delete=room_request)
            logging.info(f"------========-room-deleted========------")            

            return True
        except Exception as e:
            logging.error(f"-room-deleted=====> {e}")                        
            raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")
        finally:
            await client_session.close()
            

        
    async def update_metadata_room(self, room_name: str, metadata) -> bool:
        try:
            room_name = f"{room_name}:Talk"
            room_service, client_session = await self.get_room_service()
            room_request_update = proto_room.UpdateRoomMetadataRequest(room=room_name, metadata=metadata)
            
            room_response = await room_service.update_room_metadata(update=room_request_update)
            logging.info(f"------========-update_metadata_room-========------")            

            return True
        except Exception as e:
            logger.error(f'-erro-update_metadata_room-> {e}')
            raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")

        finally:
            await client_session.close()

@router.post("/next_period_room")
async def next_period_handler(room_name: str = Body(..., embed=True)):
    logger.info(f'--call--next-period-handler---> {room_name}')
    livekit_service = LiveKitService()
    if not room_name.endswith(':Talk'):
        room_name = f"{room_name}:Talk"
    try:
        call_id = room_name.split(':')[0]
        call = db.session.query(Call).filter(Call.id == int(call_id)).first()
        consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
        cost = consultant.video_call_cost if call.call_type == 'video' else consultant.voice_call_cost
        call.cost += cost
        end_time = call.end_time + timedelta(minutes=consultant.session_duration)
        call.end_time = end_time
        db.session.commit()
        call_metadata = {
            "call_id": call.id,
            "consultant_id": call.consultant_id,
            "client_id": call.client_id,
            "start_time": call.start_time.isoformat() if call.start_time else None, # Key change: ISO format
            "end_time": call.end_time.isoformat() if call.end_time else None,     # Key change: ISO format
            "session_duration_minutes": consultant.session_duration if consultant else None,
            "timer_active": True,
            "status": call.status
        }
        room_metadata = json.dumps(call_metadata, default=str)

        await livekit_service.update_metadata_room(call.id, room_metadata)
    except Exception as e:
        logger.error(f'-error--next_period_handler--> {e}')
        raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")
        
    


@router.post("/close-room")
async def close_room(room_name: str = Body(..., embed=True)):
    logger.info(f'--call--close--room-===============================--> {room_name}')

    livekit_service = LiveKitService()
    if not room_name.endswith(':Talk'):
        room_name = f"{room_name}:Talk"
        
    room_service, client_session = await livekit_service.get_room_service()
    delete_request = proto_room.DeleteRoomRequest(room=room_name)
    
    try:
        # ارسال درخواست حذف اتاق
        delete_response = await room_service.delete_room(delete_request)
        if delete_response:
            call_id = room_name.split(':')[0]
            if call := db.session.query(Call).filter(Call.id == int(call_id)).first():
                # تعیین وضعیت بر اساس timer_active
                if call.timer_active == True:
                    # تماس شروع شده بود، پس completed می‌شود
                    call.status = "completed"
                    call.end_time = datetime.now(timezone.utc)
                    call.timer_active = False

                    # تنظیم وضعیت مشاور به online
                    consultant = db.session.query(Consultant).filter(Consultant.id == call.consultant_id).first()
                    if consultant:
                        consultant.status = 'online'
                else:
                    # تماس هرگز شروع نشده بود
                    if call.status == 'confirmed':
                        call.status = "completed"  # اگر confirmed بود ولی timer فعال نبود
                    # اگر unconfirmed بود، همان‌طور باقی می‌ماند

                    data = {
                        'act': 'CallCanceled',
                        'notif_type': 'callCanceled',
                        'call_type': call.call_type,
                        'call_id': call.id,
                        'call_status': call.status,
                        'message_id': call.id,
                        'from_user_fullname': call.client.fullname,
                        'from_user_avatar': call.client.avatar_url,
                        'from_user_username': call.client.username,
                        'session_duration': call.consultant.session_duration,
                        'message': 'call cancel user'
                    }
                    # Use consultant's device_os for platform-specific notifications
                    consultant_platform = getattr(call.consultant, 'device_os', 'android')
                    await send_silent_notification(call.consultant.fcm, data, platform=consultant_platform)

                db.session.commit()
            return {"message": f"Successfully deleted room"}
        else:
            logger.error(f"Failed to delete room {room_name}.")
            return {"message": f"Successfully deleted room"}
            # raise HTTPException(status_code=500, detail=f"Failed to delete room {room_name}.")
    except Exception as e:
        logger.error(f"Error deleting room: {e}")
        return {"message": f"Successfully deleted room"}        
        # raise HTTPException(status_code=500, detail=f"Error deleting room: {e}")
    finally:
        await client_session.close()


